// addUpdateDevice.js
var Hjson = require(`hjson`);
require(`hjson/lib/require-config`);
var config_obj = require(`get_device_entry/device_configuration.hjson`);
var deviceConfiguration = Hjson.parse(JSON.stringify(config_obj));

const { refreshLWAaccessToken } = require(`./refreshLWAaccessToken`);
const { alexaEventAddUpdateReport } = require(`./alexaEventRequest`);
const aws_discovery = require(`discovery/aws_discovery`);
const fus_discovery = require(`discovery/fus_discovery`);
const { v4: uuidv4 } = require("uuid");
const https = require(`https`);
const get_device_entry = require(`get_device_entry`);
const secrets_manager = require(`secrets_manager`);
const db_access = require(`db_access`);
const debug = require(`debuglog`);

var translation_obj = require(`get_device_entry/translations.hjson`);
var translations = Hjson.parse(JSON.stringify(translation_obj));
var device_config_ext=require(`get_device_config`);
device_config_ext.ext_config(deviceConfiguration, translations);

async function addUpdateDevice(event, accessInformation) {
  debug.log(
    debug.FLOW,
    `*******************************************addUpdateDevice*******************************************`
  );

  var response = {
    meta: {
      statusCode: 200,
      message: JSON.stringify(`Update successful`),
    },
    data: {},
  };

  /***************************************************************************
   * Get new Access Token from LWA interface.
   **************************************************************************/
  var refreshedAccessInformation = await refreshLWAaccessToken(
    accessInformation
  );
  if (!JSON.stringify(refreshedAccessInformation).includes("UserID")) {
    debug.errorlog(`Failed to update LWA AccessToken`);
    response.meta.statusCode = 16006;
    response.meta.message = JSON.stringify(
      `No permission: need admin or superuser`
    );
    return response;
  }
  debug.log(debug.FLOW, `event: ` + JSON.stringify(event));

  /***************************************************************************
   * Retrieve information from specific device cloud (HSDP or AWS).
   **************************************************************************/
  // var deviceId = ``;
  var addupdatebody = ``;
  if (typeof event.platform_id != `undefined`) {
    if (event.platform_id === `FUS`) {
      addupdatebody = await handleFUSUpdate(event, refreshedAccessInformation);
    } else if (event.platform_id === `AWS-CH`) {
      addupdatebody = await handleAWSUpdate(event, refreshedAccessInformation);
    } else if (event.platform_id === `HSDP`) {
      addupdatebody = await handleHSDPUpdate(event, refreshedAccessInformation);
    }
  } else {
    if (event.device_id.length > 18) {
      addupdatebody = await handleAWSUpdate(event, refreshedAccessInformation);
    } else {
      addupdatebody = await handleHSDPUpdate(event, refreshedAccessInformation);
    }
  }

  if (!JSON.stringify(addupdatebody).includes("event")) {
    return addupdatebody;
  }

  // Update Pairing Table with added device
  if (event.operation_type === `ADD`) {
    await addToPairingTable(event, addupdatebody);
  }

  // debug.log(debug.FLOW, `addupdatebody: ` + JSON.stringify(addupdatebody));
  /***************************************************************************
   * Send Add / Update request to event interface.
   **************************************************************************/
  try {
    await alexaEventAddUpdateReport(
      addupdatebody,
      event,
      refreshedAccessInformation
    );
    response.data.enduser_id = event.enduser_id;
    response.data.device_id = event.device_id;
    response.data.operation_type = event.operation_type;
    return response;
  } catch (error) {
    response.meta.statusCode = 16013;
    response.meta.message = JSON.stringify(
      `No binding between user and device`
    );
    return response;
  }
}

exports.addUpdateDevice = addUpdateDevice;

async function addToPairingTable(event, addupdatebody) {
  debug.log(debug.FLOW, `Update Pairing Table with added device`);
  debug.log(debug.FLOW, `event: ` + JSON.stringify(event));
  debug.log(debug.FLOW, `addupdatebody: ` + JSON.stringify(addupdatebody));

  var deviceInfo = addupdatebody.event.payload.endpoints[0];
  let addDevice = {};

  if (deviceInfo.length != 0) {
    let ctn = deviceInfo.additionalAttributes.model;

    addDevice = {
      PHUserID: event.enduser_id,
      applianceID: deviceInfo.endpointId,
      ctn: deviceInfo.additionalAttributes.customIdentifier,
    };

    if (deviceConfiguration[ctn]) {
      let DashReplenishmentID1 = deviceConfiguration[ctn][`filter1`];
      if (DashReplenishmentID1) {
        addDevice.filter1arn = DashReplenishmentID1;
        addDevice.lifetime1 = deviceConfiguration[ctn][`lifetime1`];
      }
      let DashReplenishmentID2 = deviceConfiguration[ctn][`filter2`];
      if (DashReplenishmentID2) {
        addDevice.filter2arn = DashReplenishmentID2;
        addDevice.lifetime2 = deviceConfiguration[ctn][`lifetime2`];
      }
      let DashReplenishmentWick = deviceConfiguration[ctn][`wick`];
      if (DashReplenishmentWick) {
        addDevice.wickarn = DashReplenishmentWick;
        addDevice.lifetimewick = deviceConfiguration[ctn][`lifetimewick`];
      }

      const protocol_config = deviceConfiguration[ctn].protocol;
      if (protocol_config) {
        for (const key in protocol_config) {
          const item_config = protocol_config[key];
          const ins_config = deviceConfiguration[ctn][key + "_sensor"];
          if (
            item_config &&
            ins_config &&
            /inventorylevelsensor/i.test(item_config[0])
          ) {
            if (ins_config.lifetime) {
              if (key == "filter1") {
                addDevice.lifetime1 = ins_config.lifetime;
              }
              if (key == "filter2") {
                addDevice.lifetime2 = ins_config.lifetime;
              }
              if (key == "wick") {
                addDevice.lifetimewick = ins_config.lifetime;
              }
            }

            if (ins_config.DashReplenishmentID) {
              if (key == "filter1") {
                addDevice.filter1arn = ins_config.DashReplenishmentID;
              }
              if (key == "filter2") {
                addDevice.filter2arn = ins_config.DashReplenishmentID;
              }
              if (key == "wick") {
                addDevice.wickarn = ins_config.DashReplenishmentID;
              }
            }
          }
        }
      }
    }
  }
  debug.log(debug.FLOW, `addDevice: ` + JSON.stringify(addDevice));

  let addDevice2 = {
    TableName: `alexa_link_data`,
    Item: addDevice,
  };

  debug.log(debug.FLOW, `addDevice2: ` + JSON.stringify(addDevice2));

  let result = "";
  result = await db_access.StoreInDB(addDevice2);
  if (result === `success`) {
    debug.log(debug.FLOW, `pairing table stored succesfull`);
  } else {
    debug.errorlog(
      `UpdatePairingTable - DB store failed: ` + JSON.stringify(result)
    );
  }

  // *********************************************************************
  // Add entry for device id as Primary Key linked to user id as Sort Key
  // *********************************************************************
  let endpoint = seperateApplianceIDAndBackEnd(deviceInfo.endpointId);
  if (
    endpoint.backend === `AWS-AM` ||
    endpoint.backend === `AWS-CH` ||
    endpoint.backend === `FUS`
  ) {
    addDevice = {
      TableName: `alexa_link_data`,
      Item: {
        PHUserID: `d_` + endpoint.applianceID,
        applianceID: event.enduser_id,
      },
    };
    result = await db_access.StoreInDB(addDevice);
    if (result === `success`) {
      debug.log(debug.FLOW, `pairing table stored succesfull`);
    } else {
      debug.errorlog(
        `UpdatePairingTable - DB store failed: ` + JSON.stringify(result)
      );
    }
  }
}

function seperateApplianceIDAndBackEnd(fullApplianceId) {
  if (fullApplianceId == null) {
    return null;
  }
  var endpoint = {};

  var split = fullApplianceId.split(`_`);

  if (split.length === 2) {
    if (
      split[0] === `AWS-CH` ||
      split[0] === `AWS-AM` ||
      split[0] === `HSDP` ||
      split[0] === `FUS`
    ) {
      endpoint.backend = split[0];
    } else if (split[0] === `AWS`) {
      endpoint.backend = `AWS-AM`;
    } else {
      debug.errorlog(`ERROR - Backend ID is invalid`);
      return null;
    }
    endpoint.applianceID = split[1];
    return endpoint;
  } else {
    debug.errorlog(
      `ERROR - Endpoint ID format is not valid (Backend_applianceID)`
    );
    return null;
  }
}

async function handleFUSUpdate(event, accessInformation) {
  var deviceListFUS = ``;
  try {
    deviceListFUS = await fus_discovery.GetPairedRelationsFus(
      accessInformation.UserID
    );
  } catch (error) {
    debug.errorlog(`deviceListFUS Error: message: ${error.message}`);
    const response = {
      meta: {
        statusCode: 16013,
        message: JSON.stringify(`No binding between user and device`),
      },
      data: {},
    };
    return response;
  }

  debug.log(debug.FLOW, `FUS device list: ` + JSON.stringify(deviceListFUS));

  var addedDevice = ``;
  for (var device in deviceListFUS) {
    debug.log(
      debug.FLOW,
      `checkDevice: ` + JSON.stringify(deviceListFUS[device])
    );
    if (deviceListFUS[device].endpointId.includes(event.device_id)) {
      addedDevice = deviceListFUS[device];
      debug.log(debug.FLOW, `addedDevice: ` + JSON.stringify(addedDevice));
    }
  }

  var conversationID = uuidv4();
  const response = {
    event: {
      header: {
        namespace: "Alexa.Discovery",
        name: "AddOrUpdateReport",
        payloadVersion: "3",
        messageId: conversationID,
      },
      payload: {
        // endpoints: deviceListFUS,
        endpoints: [addedDevice],
        scope: {
          type: `BearerToken`,
          token: accessInformation.AccessToken,
        },
      },
    },
  };
  debug.log(debug.FLOW, `FUS AddOrUpdateReport: ` + JSON.stringify(response));
  return response;
}

async function handleAWSUpdate(event, accessInformation) {
  var deviceListAWS = ``;
  try {
    deviceListAWS = await aws_discovery.GetPairedRelationsAWS(
      accessInformation.UserID
    );
  } catch (error) {
    debug.errorlog(`deviceListAWS Error: message: ${error.message}`);
    const response = {
      meta: {
        statusCode: 16013,
        message: JSON.stringify(`No binding between user and device`),
      },
      data: {},
    };
    return response;
  }

  // debug.log(debug.FLOW, `AWS device list: ` + JSON.stringify(deviceListAWS));

  var addedDevice = ``;
  for (var device in deviceListAWS) {
    // debug.log(debug.FLOW, `checkDevice: ` + JSON.stringify(deviceListAWS[device]));
    if (deviceListAWS[device].endpointId.includes(event.device_id)) {
      addedDevice = deviceListAWS[device];
      // debug.log(debug.FLOW, `addedDevice: ` + JSON.stringify(addedDevice));
    }
  }

  var conversationID = uuidv4();
  const response = {
    event: {
      header: {
        namespace: "Alexa.Discovery",
        name: "AddOrUpdateReport",
        payloadVersion: "3",
        messageId: conversationID,
      },
      payload: {
        // endpoints: deviceListAWS,
        endpoints: [addedDevice],
        scope: {
          type: `BearerToken`,
          token: accessInformation.AccessToken,
        },
      },
    },
  };
  // debug.log(debug.FLOW, `AWS AddOrUpdateReport: ` + JSON.stringify(response));
  return response;
}

async function handleHSDPUpdate(event, accessInformation) {
  var deviceListHSDP = ``;
  try {
    deviceListHSDP = await GetPairedRelationsHSDP(accessInformation.UserID);
  } catch (error) {
    debug.errorlog(`deviceListHSDP Error: message: ${error.message}`);
    const response = {
      meta: {
        statusCode: 16013,
        message: JSON.stringify(`No binding between user and device`),
      },
      data: {},
    };
    return response;
  }
  // debug.log(debug.FLOW, `HSDP device list: ` + JSON.stringify(deviceListHSDP));

  var addedDevice = ``;
  for (var device in deviceListHSDP) {
    // debug.log(debug.FLOW, `checkDevice: ` + JSON.stringify(deviceListHSDP[device]));
    if (deviceListHSDP[device].endpointId.includes(event.device_id)) {
      addedDevice = deviceListHSDP[device];
      // debug.log(debug.FLOW, `addedDevice: ` + JSON.stringify(addedDevice));
    }
  }

  var conversationID = uuidv4();
  const response = {
    event: {
      header: {
        namespace: "Alexa.Discovery",
        name: "AddOrUpdateReport",
        payloadVersion: "3",
        messageId: conversationID,
      },
      payload: {
        // endpoints: deviceListHSDP,
        endpoints: [addedDevice],
        scope: {
          type: `BearerToken`,
          token: accessInformation.AccessToken,
        },
      },
    },
  };
  // debug.log(debug.FLOW, `HSDP AddOrUpdateReport: ` + JSON.stringify(response));
  return response;
}

function getUserIDandToken(userAccessToken) {
  const [userID, token] = userAccessToken.split(`_-`);
  var userIDToken = { userID: ``, token: `` };
  userIDToken.userID = userID;
  userIDToken.token = token;
  return userIDToken;
}

function prepareDeviceList(body) {
  let devicelist = [];

  // debug.log(debug.FLOW, `body: ` + JSON.stringify(body) );
  var entries = JSON.parse(body).entry;
  debug.log(debug.FLOW, `Entries: ` + JSON.stringify(entries));

  for (var number in entries) {
    var entry = entries[number];
    var parsedMetadata = JSON.parse(entry.resource.metadata);

    var applianceID = `HSDP_` + entry.resource.trustor.value;
    var name = parsedMetadata.Name;
    var ctn = parsedMetadata.ctn;
    // debug.log(debug.FLOW, `applianceID: ` + applianceID );
    // debug.log(debug.FLOW, `Name:        ` + name );
    // debug.log(debug.FLOW, `ctn:         ` + ctn );

    devicelist.push(get_device_entry.getDeviceEntry(applianceID, name, ctn));
  }

  // debug.log(debug.FLOW, `devicelist: ` + JSON.stringify(devicelist) );
  return devicelist;
}

function GetPairedRelationsHSDP(UserID) {
  return new Promise((resolve, reject) => {
    var secrets = secrets_manager.getSecretValue();
    var username = secrets.HSDP_PS_username;
    var password = secrets.HSDP_PS_password;

    var AuthorizationHeader = ``;
    AuthorizationHeader +=
      `Basic ` + Buffer.from(username + `:` + password).toString(`base64`);

    var TrusteeHeader = `urn:cphuser|` + UserID;
    var Path =
      `/PSRequestHandler/pairing/relation` +
      `?trustee=` +
      TrusteeHeader +
      `&type=control`;

    var options = {
      hostname: secrets.HSDP_PS_host_name,
      port: 443,
      path: Path,
      method: `GET`,
      timeout: 6000,
      headers: {
        Authorization: AuthorizationHeader,
        "api-version": `1`,
      },
    };
    // debug.log(debug.FLOW, `options: ` + JSON.stringify(options));

    const req = https.request(options, (response) => {
      let returnData = ``;

      response.setEncoding(`utf8`);
      response.on(`data`, (chunk) => {
        // debug.log(debug.FLOW, `OnData: ` + chunk);
        returnData += chunk;
      });

      response.on(`end`, () => {
        if (response.headers[`content-type`] === `application/json`) {
          returnData = JSON.parse(returnData);
        }
        var deviceList = prepareDeviceList(returnData);
        // debug.log(debug.FLOW, `deviceList:_` + JSON.stringify(deviceList));
        resolve(deviceList);
      });
      response.on(`error`, (error) => {
        debug.errorlog(`ERROR: ` + error);
        reject(error);
      });
    });
    req.on(`timeout`, () => {
      debug.errorlog(`Timeout`);
      req.abort();
      reject(new Error(`Timeout`));
    });
    req.on(`error`, (error) => {
      debug.errorlog(`req error: ` + error);
      reject(new Error(`Req Error` + error));
    });

    req.write(`data\n`);
    req.end();

    // debug.log(debug.FLOW, `return GetPairedRelationsHSDP`);
  });
}
