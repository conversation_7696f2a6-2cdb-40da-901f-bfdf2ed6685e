const support_functions = require(`support_functions`);
const {GetPairedRelationsHSDP} = require(`./hsdp_discovery`);
const {GetPairedRelationsAWS} = require(`./aws_discovery`);
const secrets_manager = require(`secrets_manager`);
const debug = require(`debuglog`);


async function handleDiscovery(request, callback) {

    const userAccessToken = request.directive.payload.scope.token.trim();

    // await debug.getDebugLevel(request);
    debug.log(debug.FLOW, `Discovery Request: ` + JSON.stringify(request));

    /*
     *  Retrieve secrets from vault.
     */
    var secrets = await secrets_manager.getSecretsFromManager(`SmartHomeSecrets`);
    if (!('AWS_host_name' in secrets)) {
        // Failed to retrieve secrets from vault. Return error to Alexa.
        debug.errorlog(`Discovery - Failed to get Secrets`);
        callback(null, support_functions.generateErrorResponse(`INTERNAL_ERROR`, `Unable to get Secrets`, request));
        return;
    }

    if (!userAccessToken || !support_functions.isValidToken(userAccessToken)) {
        const errorMessage = `Request [${request.header.messageId}] failed. Invalid access token: ${userAccessToken}`;
        debug.errorlog(`Discovery - ` + errorMessage);
        callback(null, support_functions.generateErrorResponse(`ENDPOINT_UNREACHABLE`, `Unable to set device mode`, request));
        return;
    }

    var deviceListHSDP = [];
    try {
        deviceListHSDP = await GetPairedRelationsHSDP(userAccessToken);
    }
    catch (error) {
        debug.errorlog(`Discovery - DeviceListHSDP Error: message: ${error.message}`);
        // TODO Check how to handle this situation.
        // callback(null, support_functions.generateErrorResponse(`ENDPOINT_UNREACHABLE`, `Unable to set device mode`, request));
        // return;
    }

    var deviceListAWS = [];
    try {
        deviceListAWS = await GetPairedRelationsAWS(userAccessToken);
    }
    catch (error) {
        debug.errorlog(`Discovery - DeviceListAWS Error: message: ${error.message}`);
        // TODO Check how to handle this situation.
        // callback(null, support_functions.generateErrorResponse(`ENDPOINT_UNREACHABLE`, `Unable to set device mode`, request));
        // return;
    }

    var deviceList = deviceListHSDP.concat(deviceListAWS);

    const response = {
        event: {
            header: {
                namespace: "Alexa.Discovery",
                name: "Discover.Response",
                payloadVersion: "3",
                messageId: request.directive.header.messageId,
            },
            payload: {
                endpoints: deviceList,
            },
        },
    };

    debug.log(debug.DISCOVERY, `Discovery - Response: ` + JSON.stringify(response));
    callback(null, response);

}

module.exports = {
    handleDiscovery
}

