const get_device_entry = require(`get_device_entry`);
const secrets_manager = require(`secrets_manager`);
const debug = require(`debuglog`);
const fusion_api = require(`fus_api`);
const tokenHandler = require(`token`);
const encrypt = require(`encrypt`);

async function GetPairedRelationsFus(token) {
    try {
        var deviceList = await GetFusPairedDevices(token);
        debug.log (debug.DISCOVERY, `FUS_Discovery - FusDeviceList: ` + JSON.stringify(deviceList));
        return(deviceList);
    }
    catch (error) {
        debug.errorlog(`Discovery - FusDeviceList Error: message: ${error.message}`);
        return(error);
    }
}

exports.GetPairedRelationsFus = GetPairedRelationsFus;


async function enableExternalControlService(deviceId, token) {
    var secrets = secrets_manager.getSecretValue();

    var base_url = secrets.FUS_BaseURI;
    var skill_id = secrets.alexa_skill_id;
    var uriPath = `/api/da/control/link/` + deviceId;
    const options = {
        hostname: base_url,
        path: uriPath,
        method: `POST`,
        timeout: 6000,
        headers: {
            Authorization: `Bearer ` + token.UDI_access_token
        }
    };
    var body = {
        "skillId": skill_id,
        "service": "alexa",
        "ports": [
            "Status",
            "filtRd"
        ]
    };
    debug.log (debug.DISCOVERY, `EnableExternalControlService - options: ` + JSON.stringify(options, null, 4));
    debug.log (debug.DISCOVERY, `EnableExternalControlService - body: ` + JSON.stringify(body, null, 4));

    try {
        var [response, responseData] = await fusion_api.fus_api (options, JSON.stringify(body));
        debug.log (debug.DISCOVERY, `FUS_Discovery - EnableExternalControlService - responseData: ` + JSON.stringify(responseData, null, 4));
        debug.log (debug.DISCOVERY, `EnableExternalControlService - response.statusCode: ` + response.statusCode);
        if (response.statusCode === 204) {
            return true;
        }
        else {
            debug.errorlog(`EnableExternalControlService - Error response.statusCode: ` + response.statusCode);
            return false;
        }
    } catch (error) {
        debug.errorlog(`EnableExternalControlService - fus_api Error: message: ${error.message}`);
        return(false);
    }
}

async function prepareDeviceList(deviceListResponse, token) {
    let devicelist = [];
    debug.log (debug.DISCOVERY, `FUS_Discovery - prepare deviceListResponse:\n` + JSON.stringify(deviceListResponse) );

    for (let device of deviceListResponse) {
        var applianceID = `FUS_` + device.id;
        var name = ``;
        if (typeof device.friendlyName == `undefined`) {
            name = device.ctn;
        } else {
            name = device.friendlyName;
        }
        var ctn = device.ctn;
        var result;
        debug.log (debug.DISCOVERY, `FUS_Discovery - applianceID: ` + applianceID + `\nName:        ` + name + `\nctn:         ` + ctn );

        // result = await disableExternalControlService(device.id, token);

        result = await enableExternalControlService(device.id, token);
        if (result == true) {
            devicelist.push(get_device_entry.getDeviceEntry(applianceID, name, ctn));
        }
    }
    return devicelist;
}







async function GetFusPairedDevices(UserID) {
    var secrets = secrets_manager.getSecretValue();

    var tokensFromTable = await tokenHandler.getTokensFromTable(UserID);
    var token = {};
    try {
        debug.log (debug.DISCOVERY, `GetFusPairedRelations - tokensFromTable: ` + JSON.stringify(tokensFromTable, null, 4));
        let tableToken = tokensFromTable.Items.filter(tk => tk.token_type === `OneID_token`)[0];
        debug.log (debug.DISCOVERY, `GetFusPairedRelations - tableToken: ` + JSON.stringify(tableToken, null, 4));
        if (typeof tableToken == `undefined`) {
            // AWSToken = await aws_gettoken.GetAWSToken(UserID, time, `AWS-AM`);
            // await tokenHandler.storeTokensToTable(UserID, `AWS-AM_token`, AWSToken, (time + 3600) );      // TimeToLive is the time of the request + the lifetime of the token (3600)
        } else {
            token.UDI_access_token = tableToken.access_token;
          
        }
        debug.log (debug.DISCOVERY, `GetFusPairedRelations - UDI_access_token: ` + token.UDI_access_token);
    }
    catch (error) {
        debug.errorlog(`Discovery - GetFUSToken Error: message: ${error.message}`);
        return(error);
    }


    var base_url = secrets.FUS_BaseURI;
    var uriPath = `/api/da/control/devicelist`;
    const options = {
        hostname: base_url,
        path: uriPath,
        method: `GET`,
        timeout: 6000,
        headers: {
            Authorization: `Bearer ` + token.UDI_access_token
        }
    };
    debug.log (debug.DISCOVERY, `getDeviceList - options: ` + JSON.stringify(options, null, 4));

    try {
        var [response, responseData] = await fusion_api.fus_api (options, ``);
        debug.log (debug.DISCOVERY, `FUS_Discovery - responseData: ` + responseData);
        debug.log (debug.DISCOVERY, `FUS_Discovery - response.statusCode: ` + response.statusCode);
        if (response.statusCode === 200) {
            let responseDataParsed = JSON.parse(responseData);
            if (responseDataParsed.length != 0) {
                let deviceList = await prepareDeviceList(responseDataParsed, token);
                debug.log (debug.DISCOVERY, `FUS_Discovery - deviceList: ` + JSON.stringify (deviceList));
                return deviceList;
            }
            else {
                // No devices connected -> return emtpy list.
                return ``;
            }
        }
        else {
            debug.errorlog(`FUS_Discovery - Error response.statusCode: ` + response.statusCode);
            return ``;
        }
    } catch (error) {
        debug.errorlog(`Discovery - fus_api Error: message: ${error.message}`);
        return(error);
    }


}



async function disableExternalControlService(deviceId, token) {
    var secrets = secrets_manager.getSecretValue();

    var base_url = secrets.FUS_BaseURI;
    var uriPath = `/api/da/control/link/` + deviceId + `/alexa/` + secrets.alexa_skill_id;
    const options = {
        hostname: base_url,
        path: uriPath,
        method: `DELETE`,
        timeout: 6000,
        headers: {
            Authorization: `Bearer ` + token.UDI_access_token
        }
    };
    var body = ``;
    debug.log (debug.DISCOVERY, `disableExternalControlService - options: ` + JSON.stringify(options, null, 4));
    debug.log (debug.DISCOVERY, `disableExternalControlService - body: ` + JSON.stringify(body, null, 4));

    try {
        var [response, responseData] = await fusion_api.fus_api (options, body);
        // debug.log (debug.DISCOVERY, `FUS_Discovery - disableExternalControlService - responseData: ` + responseData);
        debug.log (debug.DISCOVERY, `FUS_Discovery - disableExternalControlService - response.statusCode: ` + response.statusCode);
        if (response.statusCode === 204) {
            return true;
        }
        else {
            debug.errorlog(`disableExternalControlService - Error response.statusCode: ` + response.statusCode);
            return false;
        }
    } catch (error) {
        debug.errorlog(`disableExternalControlService - fus_api Error: message: ${error.message}`);
        return(false);
    }
}
