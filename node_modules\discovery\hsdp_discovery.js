const https = require(`https`);
const get_device_entry = require(`get_device_entry`);
const secrets_manager = require(`secrets_manager`);
const debug = require(`debuglog`);
const tokenHandler = require(`token`);



function prepareDeviceList(body) {
    let devicelist = [];

    debug.log (debug.DISCOVERY, `HSDP_Discovery - prepare devicelist body:\n` + JSON.stringify(body) );
    try {
        var entries = JSON.parse(body).entry;
    }
    catch (error) {
        debug.errorlog(`Discovery - parsing device entry failed: ` + error.message);
        return devicelist;
    }
    debug.log (debug.DISCOVERY, `HSDP_Discovery - prepare devicelist Entries:\n` + JSON.stringify(entries) );

    for (var number in entries) {
        try {
            var entry = entries[number];
            var parsedMetadata = JSON.parse(entry.resource.metadata);

            var applianceID = `HSDP_` + entry.resource.trustor.value;
            var name = parsedMetadata.Name;
            var ctn = parsedMetadata.ctn;
            debug.log (debug.DISCOVERY, `HDSP_Discovery - applianceID: ` + applianceID + `\nName:        ` + name + `\nctn:         ` + ctn );

            devicelist.push(get_device_entry.getDeviceEntry(applianceID, name, ctn));
        }
        catch (error) {
            debug.errorlog(`Discovery - parsing device entry failed: ` + error.message);
        }


    }

    debug.log (debug.DISCOVERY, `HDSP_Discovery - devicelist:\n` + JSON.stringify(devicelist) );
    return devicelist;
}

async function GetPairedRelationsHSDP(UserID) {

    // var userIdToken = getUserIDandToken(userAccessToken);
    try {
        var userIdToken = getHSDPToken(UserID);
        debug.log (debug.DISCOVERY, `GetPairedRelationsHSDP - userIdToken: ` + JSON.stringify(userIdToken));
    }
    catch (error) {
        debug.errorlog(`Discovery - GetPairedRelationsHSDP Error: message: ${error.message}`);
        return(error);
    }


    try {
        return await GetHSDPRequest(userIdToken);
    } catch (error) {
        return error;
    }

}

async function GetHSDPRequest(userIdToken) {
    return new Promise((resolve, reject) => {
        var secrets = secrets_manager.getSecretValue();


        var AuthorizationHeader = `cphuser uuid="` + userIdToken.userID + `",token="` + userIdToken.token + `"`;
        var TrusteeHeader = `urn:cphuser|` + userIdToken.userID;

        var options = {
            hostname: secrets.HSDP_PS_host_name,
            port: 443,
            path: `/PSRequestHandler/pairing/relation`,
            method: `GET`,
            timeout: 6000,
            headers: {
                'Content-Type': `application/json`,
                'Authorization': AuthorizationHeader,
                'api-version': `1`,
                'trustee': TrusteeHeader,
                'type': `control`
            }
        };
        debug.log (debug.DISCOVERY, `HDSP_Discovery - HSDP options: ` + JSON.stringify(options));

        const req = https.request(options, (response) => {
            let returnData = ``;

            response.setEncoding(`utf8`);
            response.on(`data`, (chunk) => {
                // debug.log (debug.DISCOVERY, `HSDP_Discovery - OnData: ` + chunk);
                returnData += chunk;
            });

            response.on(`end`, () => {
                if (response.headers[`content-type`] === `application/json`) {
                    returnData = JSON.parse(returnData);
                }
                var deviceList = prepareDeviceList(returnData);
                resolve(deviceList);
            });
            response.on(`error`, (error) => {
                debug.errorlog(`Discovery - General Error: ` + error);
                reject(error);
            });

        });
        req.on(`timeout`, () => {
            debug.errorlog(`Discovery - Timeout`);
            req.abort();
            reject(new Error(`Timeout`));
        });
        req.on(`error`, (error) => {
            debug.errorlog(`Discovery - Req error: ` + error);
            reject(new Error(`Req Error` + error));
        });

        req.write(`data\n`);
        req.end();

        debug.log (debug.DISCOVERY, `HDSP_Discovery - return GetPairedRelationsHSDP`);
    });
}

exports.GetPairedRelationsHSDP = GetPairedRelationsHSDP;





async function getHSDPToken(UserID) {
    // return userIdToken.userID , userIdToken.token


    var time = Math.round(+new Date()/1000);
    var userIdToken = {
        userID: UserID,
        token: ""
    };
    var tokensFromTable = await tokenHandler.getTokensFromTable(UserID);

    let tableToken = tokensFromTable.Items.filter(tk => tk.token_type === `HSDP_token`)[0];
    if (typeof tableToken == `undefined`) {
        let userinfo = await tokenHandler.getUserInfo(tokensFromTable.UDI_access_token);
        if (typeof (userinfo.email) != `undefined`) {
            let emailaddress = userinfo.email;
            let HSDPresponse = await tokenHandler.getHSDPtoken(tokensFromTable.UDI_access_token, emailaddress);
            if (typeof(HSDPresponse.exchange.accessCredential.accessToken) != `undefined`) {
                userIdToken.token = HSDPresponse.exchange.accessCredential.accessToken;
                await tokenHandler.storeTokensToTable(tokensFromTable.uuid, `HSDP_token`, userIdToken.token, (time + 3600) );      // TimeToLive is the time of the request + the lifetime of the token (3600)
            }
        }
    }
    else if (  (typeof tableToken.ttl != `undefined`) && (tableToken.ttl <= (time + 60)) ) {
        let userinfo = await tokenHandler.getUserInfo(tokensFromTable.UDI_access_token);
        if (typeof (userinfo.email) != `undefined`) {
            let emailaddress = userinfo.email;
            let HSDPresponse = await tokenHandler.getHSDPtoken(tableToken.UDI_access_token, emailaddress);
            if (typeof(HSDPresponse.exchange.accessCredential.accessToken) != `undefined`) {
                userIdToken.token = HSDPresponse.exchange.accessCredential.accessToken;
                await tokenHandler.storeTokensToTable(UserID, `HSDP_token`, userIdToken.token, (time + 3600) );      // TimeToLive is the time of the request + the lifetime of the token (3600)
            }
        }
    }
    else {
        userIdToken.token = tableToken.access_token;
    }
    debug.log (debug.TOKEN, `getHSDPToken - userIdToken: ` + JSON.stringify(userIdToken.token, null, 4));
    return userIdToken;
}
