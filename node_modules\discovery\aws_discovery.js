const https = require(`https`);
const get_device_entry = require(`get_device_entry`);
const aws_gettoken = require(`aws_gettoken`);
const secrets_manager = require(`secrets_manager`);
const debug = require(`debuglog`);
const tokenHandler = require(`token`);


function prepareDeviceList(body, backend) {
    let devicelist = [];

    debug.log (debug.DISCOVERY, `AWS_Discovery - prepare devicelist body:\n` + JSON.stringify(body) );

    for (var number in body) {
        var entry = body[number];
        var precursor = ``;
        
        if (backend === `AWS-CH`) {
            precursor = `AWS-CH_`;
        } else {
            precursor = `AWS-AM_`;
        }

        var applianceID = precursor + entry.device_id;
        var name = entry.device_info.name;
        var ctn = entry.device_info.modelid;
        debug.log (debug.DISCOVERY, `AWS_Discovery - applianceID: ` + applianceID + `\nName:        ` + name + `\nctn:         ` + ctn );
    
        devicelist.push(get_device_entry.getDeviceEntry(applianceID, name, ctn));
        number ++;
    }

    return devicelist;
}

function concatAWSLists (devCH, devAM) {
    var newList = [];
    devCH.forEach(function (item) {
        newList.push(item);
    });
    for (var dev in devAM) {
        let devEndPID = devAM[dev].endpointId.split('_');
        let endPID = 'AWS-CH_' + devEndPID[1];
        if (! devCH.some(el => el.endpointId == endPID)) {
            newList.push(devAM[dev]);
        }
    }
    return newList;
}

async function GetPairedRelationsAWS(UserID) {
        var time = Math.round(+new Date()/1000);
        var deviceList = ``;
        var deviceList_CH = ``;
        var deviceList_AM = ``;
        var AWSToken = ``;

        var tokensFromTable = await tokenHandler.getTokensFromTable(UserID);

        try {
            let tableToken = tokensFromTable.Items.filter(tk => tk.token_type === `AWS-AM_token`)[0];
            if (typeof tableToken == `undefined`) {
                AWSToken = await aws_gettoken.GetAWSToken(UserID, time, `AWS-AM`);
                await tokenHandler.storeTokensToTable(UserID, `AWS-AM_token`, AWSToken, (time + 3600) );      // TimeToLive is the time of the request + the lifetime of the token (3600)
            }
            else if ( (typeof tableToken.ttl != `undefined`) && (tableToken.ttl <= (time + 60)) ) {
                AWSToken = await aws_gettoken.GetAWSToken(UserID, time, `AWS-AM`);
                await tokenHandler.storeTokensToTable(UserID, `AWS-AM_token`, AWSToken, (time + 3600) );      // TimeToLive is the time of the request + the lifetime of the token (3600)
            } else {
                AWSToken = tableToken.access_token;
            }
            // debug.log (debug.DISCOVERY, `GetPairedRelationsAWS - AWSToken_AM: ` + AWSToken);
        }
        catch (error) {
            debug.errorlog(`Discovery - GetAWSToken_AM Error: message: ${error.message}`);
            return(error);
        }

        try {
            deviceList_AM = await GetAWSPairedDevices(AWSToken, `AWS-AM`);
            debug.log (debug.DISCOVERY, `AWS_Discovery - AWSdeviceList_AM: ` + JSON.stringify(deviceList_AM));
        }
        catch (error) {
            debug.errorlog(`Discovery - AWSdeviceList_AM Error: message: ${error.message}`);
            return(error);
        }



        try {
            let tableToken = tokensFromTable.Items.filter(tk => tk.token_type === `AWS-CH_token`)[0];
            if (typeof tableToken == `undefined`) {
                AWSToken = await aws_gettoken.GetAWSToken(UserID, time, `AWS-CH`);
                await tokenHandler.storeTokensToTable(UserID, `AWS-CH_token`, AWSToken, (time + 3600) );      // TimeToLive is the time of the request + the lifetime of the token (3600)
            }
            else if ( (typeof tableToken.ttl != `undefined`) && (tableToken.ttl <= (time + 60)) ) {
                AWSToken = await aws_gettoken.GetAWSToken(UserID, time, `AWS-CH`);
                await tokenHandler.storeTokensToTable(UserID, `AWS-CH_token`, AWSToken, (time + 3600) );      // TimeToLive is the time of the request + the lifetime of the token (3600)
            } else {
                AWSToken = tableToken.access_token;
            }
            // debug.log (debug.DISCOVERY, `GetPairedRelationsAWS - AWSToken_CH: ` + AWSToken);
        }
        catch (error) {
            debug.errorlog(`Discovery - GetAWSToken_CH Error: message: ${error.message}`);
            return(error);
        }

        try {
            deviceList_CH = await GetAWSPairedDevices(AWSToken, `AWS-CH`);
            debug.log (debug.DISCOVERY, `AWS_Discovery - AWSdeviceList_CH: ` + JSON.stringify(deviceList_CH));
            deviceList = concatAWSLists(deviceList_CH, deviceList_AM);
            return(deviceList);
        }
        catch (error) {
            debug.errorlog(`Discovery - AWSdeviceList_CH Error: message: ${error.message}`);
            return(error);
        }
}

exports.GetPairedRelationsAWS = GetPairedRelationsAWS;



function GetAWSPairedDevices(enduserToken, backend) {
    return new Promise((resolve, reject) => {
        var secrets = secrets_manager.getSecretValue();

        var bodydata = `data`;
        var authorizationHeader = `JWT ` + enduserToken;

        var options = {
            hostname: secrets.AWS_host_name,
            port: 443,
            path: `/enduser/v2/deviceList/`,
            method: `GET`,
            timeout: 6000,
            headers: {
                'Content-Type': `application/json`,
                'Authorization': authorizationHeader
            }
        };
        // debug.log (debug.DISCOVERY, `GetAWSPairedDevices - Options: ` + JSON.stringify(options, null, 4));

        const req = https.request(options, (response) => {
            let responseData = ``;
            let returnDataValid = false;
            var deviceList = ``;

            response.setEncoding(`utf8`);
            response.on(`data`, (chunk) => {
                // debug.log (debug.DISCOVERY, `GetAWSPairedDevices - OnData: ` + chunk);
                responseData += chunk;
            });

            response.on(`end`, () => {
                // debug.log (debug.DISCOVERY, `GetAWSPairedDevices - OnEnd - response.statusCode: ` + JSON.stringify (response.statusCode));
                if (response.statusCode === 200) {
                    if (response.headers[`content-type`] === `application/json`) {

                        // debug.log (debug.DISCOVERY, `GetAWSPairedDevices - OnEnd - responseData: ` + JSON.stringify (responseData, null, 4));

                        responseData = JSON.parse(responseData);
                        if (responseData.hasOwnProperty(`meta`)) {
                            if (responseData.meta.hasOwnProperty(`code`)) {
                                if (responseData.meta.code === 0) {
                                    // Data is accepted, token string is valid, return token.
                                    // debug.log (debug.DISCOVERY, `GetAWSPairedDevices - prepareDeviceList - responseData.data: ` + JSON.stringify (responseData.data, null, 4));
                                    deviceList = prepareDeviceList(responseData.data, backend);
                                    returnDataValid = true;
                                }
                                else {
                                    debug.errorlog(`GetAWSPairedDevices - Error in MetaData3: ` + JSON.stringify(responseData));
                                }
                            }
                            else {
                                debug.errorlog(`GetAWSPairedDevices - Error in MetaData2: ` + JSON.stringify(responseData));
                            }
                        }
                        else {
                            debug.errorlog(`GetAWSPairedDevices - Error in MetaData1: ` + JSON.stringify(responseData));
                        }
                    }
                    else {
                        debug.errorlog(`GetAWSPairedDevices - Error on header: ` + responseData);
                    }
                }
                else {
                    debug.errorlog(`GetAWSPairedDevices - Error response.statusCode: ` + response.statusCode);
                }
                if (returnDataValid === false) {
                    reject(new Error(`Invalid DeviceList Response`));
                } else {
                    resolve(deviceList);
                }
            });
            response.on(`error`, (error) => {
                debug.errorlog(`GetAWSPairedDevices - General Error: ` + error);
                reject(error);
            });

        });
        req.on(`timeout`, () => {
            debug.errorlog(`GetAWSPairedDevices - Timeout`);
            req.abort();
            reject(new Error(`Timeout`));
        });
        req.on(`error`, (error) => {
            debug.errorlog(`GetAWSPairedDevices - Req error: ` + error);
            reject(new Error(`Req Error` + error));
        });
    
        req.write(bodydata);
        req.end();
    
        // debug.log (debug.DISCOVERY, `AWS_Discovery - Return GetAWSPairedDevices`);
    });
}

