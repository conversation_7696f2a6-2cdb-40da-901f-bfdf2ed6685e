// Version 2.2.0.0

const { updateDiscovery } = require(`./updateDiscovery`);


exports.handler = async (event) => {
    console.log('updateDiscovery.handler 20250519');
    console.log(`event: ` + JSON.stringify(event));
    var response = {
        meta: {
            statusCode: 200,
            message: JSON.stringify(``)
        },
        data :{
        }
    };

    /***************************************************************************
     * Validate parameters.
     **************************************************************************/
    if (!event.hasOwnProperty(`enduser_id`)) {
        console.log(`no enduser_id found in event`);
        response.meta.statusCode = 160001;
        response.meta.message = JSON.stringify(`Invalid enduser_id`);
        return response;
    }
    if (!event.hasOwnProperty(`device_id`)) {
        console.log(`no device_id found in event`);
        response.meta.statusCode = 160003;
        response.meta.message = JSON.stringify(`Invalid device_id`);
        return response;
    }
    if (!event.hasOwnProperty(`operation_type`)) {
        console.log(`no operation_type found in event`);
        response.meta.statusCode = 160012;
        response.meta.message = JSON.stringify(`Invalid operation_type`);
        return response;
    }

    /***************************************************************************
     * Start updating device list in Alexa discovery.
     **************************************************************************/
    response = await updateDiscovery(event);

    return response; 
};
